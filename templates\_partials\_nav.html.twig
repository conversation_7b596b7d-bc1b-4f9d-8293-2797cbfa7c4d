{# templates/navbar.html.twig #}
{% set current_route = app.request.attributes.get('_route') %}
{% set current_place = app.request.attributes.get('place') %}
{# Temporairement désactivé pour debug: {% set badges = navbar_badges() %} #}
{% set badges = {} %}

<nav id="navBar" class="navbar navbar-expand-lg" style="background-color: #004080;">
    <div class="container-fluid">
        <a class="navbar-brand p-0 parent_logo" href="{{ path('app_home') }}">
            <img src="{{ asset('icon.png') }}" alt="logo" height="30" class="logo_scm d-inline-block align-text-top p-0">
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                data-bs-target="#navbarSupportedContent"
                aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- La zone "collapse" qui contient tous les liens -->
        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <!-- Liste principale -->
            <ul class="navbar-nav me-auto w-100 mb-2 mb-lg-0 d-flex align-items-center justify-content-center">

                {# Packages – route spécifique #}
                <li class="nav-item {{ current_route == 'app_package' ? 'active' : '' }}" data-step="Packages">
                    <a class="nav-link {{ current_route == 'app_package' ? 'active' : '' }}" href="{{ path('app_package') }}">Packages</a>
                </li>

                {# Produit – même route avec paramètre "place" #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Produit' ? 'active' : '' }}" data-step="Produit">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Produit' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Produit'}) }}">
                        Product Management
                    </a>
                </li>

                {# Inventory (Qual_Logistique) – on considère aussi "Logistique" #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place in ['Qual_Logistique','Logistique'] ? 'active' : '' }}" data-step="Qual_Logistique">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place in ['Qual_Logistique','Logistique'] ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Qual_Logistique'}) }}">
                        Inventory
                    </a>
                </li>

                {# Quality #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Quality' ? 'active' : '' }}" data-step="Quality">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Quality' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Quality'}) }}">
                        Quality
                    </a>
                </li>

                {# Project #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Project' ? 'active' : '' }}" data-step="Project">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Project' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Project'}) }}">
                        Project
                    </a>
                </li>

                {# Metro #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Metro' ? 'active' : '' }}" data-step="Metro">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Metro' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Metro'}) }}">
                        Metrologie
                    </a>
                </li>

                {# QProd #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'QProd' ? 'active' : '' }}" data-step="QProd">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'QProd' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'QProd'}) }}">
                        Quality Prod
                    </a>
                </li>

                {# Dropdown Prod – pour Assembly, Machining, Molding #}
                <li class="nav-item dropdown {{ current_route == 'app_document_place' and current_place in ['Assembly','Machining','Molding'] ? 'active' : '' }}">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place in ['Assembly','Machining','Molding'] ? 'active' : '' }} dropdown-toggle" href="#" role="button" id="dropdownMenuProd" data-bs-toggle="dropdown" aria-expanded="false">
                       Prod
                    </a>
                    <ul class="dropdown-menu p-1" aria-labelledby="dropdownMenuProd">
                        <li data-step="Assembly" class="{{ current_route == 'app_document_place' and current_place == 'Assembly' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Assembly'}) }}">
                                Assembly
                            </a>
                        </li>
                        <li data-step="Machining" class="{{ current_route == 'app_document_place' and current_place == 'Machining' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Machining'}) }}">
                                Machining
                            </a>
                        </li>
                        <li data-step="Molding" class="{{ current_route == 'app_document_place' and current_place == 'Molding' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Molding'}) }}">
                                Molding
                            </a>
                        </li>
                    </ul>
                </li>
                
                {# Tirage Plans #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Tirage_Plans' ? 'active' : '' }}" data-step="Tirage_Plans">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Tirage_Plans' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Tirage_Plans'}) }}">
                        Tirage Plans
                    </a>
                </li>


                {# Assemblage – Methode_assemblage #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Methode_assemblage' ? 'active' : '' }}" data-step="Methode_assemblage">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Methode_assemblage' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Methode_assemblage'}) }}">
                        Assy Method.
                    </a>
                </li>

                {# Dropdown Achats – pour Achat_Rfq, Achat_F30, Achat_FIA, Achat_RoHs_REACH, Achat_Hts #}
                <li class="nav-item dropdown {{ current_route == 'app_document_place' and current_place in ['Achat_Rfq','Achat_F30','Achat_FIA','Achat_RoHs_REACH','Achat_Hts'] ? 'active' : '' }}">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place in ['Achat_Rfq','Achat_F30','Achat_FIA','Achat_RoHs_REACH','Achat_Hts'] ? 'active' : '' }} dropdown-toggle" href="#" role="button" id="dropdownMenuAchats" data-bs-toggle="dropdown" aria-expanded="false">
                       Achats
                    </a>
                    <ul class="dropdown-menu p-1" aria-labelledby="dropdownMenuAchats">
                        <li data-step="Achat_Rfq" class="{{ current_route == 'app_document_place' and current_place == 'Achat_Rfq' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Achat_Rfq'}) }}">
                                RFQ
                            </a>
                        </li>
                        <li data-step="Achat_F30" class="{{ current_route == 'app_document_place' and current_place == 'Achat_F30' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Achat_F30'}) }}">
                                F30
                            </a>
                        </li>
                        <li data-step="Achat_FIA" class="{{ current_route == 'app_document_place' and current_place == 'Achat_FIA' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Achat_FIA'}) }}">
                                FIA
                            </a>
                        </li>
                        <li data-step="Achat_RoHs_REACH" class="{{ current_route == 'app_document_place' and current_place == 'Achat_RoHs_REACH' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Achat_RoHs_REACH'}) }}">
                                RoHs REACH
                            </a>
                        </li>
                        <li data-step="Achat_Hts" class="{{ current_route == 'app_document_place' and current_place == 'Achat_Hts' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Achat_Hts'}) }}">
                                HTS
                            </a>
                        </li>
                    </ul>
                </li>

                {# Planning #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Planning' ? 'active' : '' }}" data-step="Planning">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Planning' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Planning'}) }}">
                        Logistics
                    </a>
                </li>

                {# Indus #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Indus' ? 'active' : '' }}" data-step="Indus">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Indus' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Indus'}) }}">
                        Assy. Routings
                    </a>
                </li>

                {# Dropdown SAP – pour Core_Data et Prod_Data #}
                <li class="nav-item dropdown {{ current_route == 'app_document_place' and current_place in ['Core_Data','Prod_Data'] ? 'active' : '' }}">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place in ['Core_Data','Prod_Data'] ? 'active' : '' }} dropdown-toggle" href="#" role="button" id="dropdownMenuSAP" data-bs-toggle="dropdown" aria-expanded="false">
                       SAP
                    </a>
                    <ul class="dropdown-menu p-1" aria-labelledby="dropdownMenuSAP">
                        <li data-step="Core_Data" class="{{ current_route == 'app_document_place' and current_place == 'Core_Data' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Core_Data'}) }}">
                                Core Data
                            </a>
                        </li>
                        <li data-step="Prod_Data" class="{{ current_route == 'app_document_place' and current_place == 'Prod_Data' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Prod_Data'}) }}">
                                Prod Data
                            </a>
                        </li>
                    </ul>
                </li>

                {# Labo – méthode labo #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'methode_Labo' ? 'active' : '' }}" data-step="methode_Labo">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'methode_Labo' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'methode_Labo'}) }}">
                        Laboratory
                    </a>
                </li>

                {# GID #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'GID' ? 'active' : '' }}" data-step="GID">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'GID' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'GID'}) }}">
                        Routing Entry
                    </a>
                </li>

                {# Costing #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Costing' ? 'active' : '' }}" data-step="Costing">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Costing' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Costing'}) }}">
                        Costing
                    </a>
                </li>

                {# Dropdown Gestion – pour Retour et Suivi des packages #}
                <li class="nav-item dropdown {{ current_route in ['retour','suivie_ref'] ? 'active' : '' }}">
                    <a class="nav-link {{ current_route in ['retour','suivie_ref'] ? 'active' : '' }} dropdown-toggle" href="#" role="button" id="dropdownMenuGestion" data-bs-toggle="dropdown" aria-expanded="false">
                       Gestion
                    </a>
                    <ul class="dropdown-menu p-1" aria-labelledby="dropdownMenuGestion">
                        <li data-step="retour" class="{{ current_route == 'retour' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('retour') }}">Retour</a>
                        </li>
                        <li data-step="suivie_ref" class="{{ current_route == 'suivie_ref' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('suivie_ref') }}">Suivi des packages</a>
                        </li>
                        <li data-step="time_tracking" class="{{ current_route == 'app_time_tracking' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_time_tracking') }}">Suivi des temps</a>
                        </li>
                        <li class="dropdown-divider"></li>
                        <li data-step="statistics" class="{{ current_route == 'app_statistics' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_statistics') }}">
                                <i class="fas fa-chart-line me-2"></i>Statistiques
                            </a>
                        </li>
                    </ul>
                </li>

                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="dropdownMenuTimeStats" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-clock"></i> Temps
                    </a>
                    {{ render(controller('App\\Controller\\TimeTrackingController::navbarStats')) }}
                </li>

            </ul>
        </div>

        <div class="d-flex align-items-center">
            <ul class="navbar-nav me-3">
                <li class="nav-item">
                    <a class="nav-link d-flex align-items-center" href="{{ path('app_dashboard') }}" title="Tableau de bord">
                        <span class="nav-icon-wrapper">
                            <i class="fas fa-tachometer-alt"></i>
                        </span>
                        <span class="d-none d-lg-inline ms-1">Tableau de bord</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link d-flex align-items-center" href="{{ path('app_forecast') }}" title="Prévisions">
                        <span class="nav-icon-wrapper">
                            <i class="fas fa-chart-line"></i>
                        </span>
                        <span class="d-none d-lg-inline ms-1">Prévisions</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link d-flex align-items-center" href="{{ path('app_user_managed_places') }}" title="Gérer mes places">
                        <span class="nav-icon-wrapper">
                            <i class="fas fa-tasks"></i>
                        </span>
                        <span class="d-none d-lg-inline ms-1">Mes places</span>
                    </a>
                </li>
            </ul>
            <style>
                .nav-icon-wrapper {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    width: 24px;
                    height: 24px;
                    position: relative;
                }

                .navbar-nav .nav-link {
                    padding: 0.5rem 0.75rem;
                    border-radius: 4px;
                    transition: all 0.2s ease;
                }

                .navbar-nav .nav-link:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                }
            </style>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="aletiq">
                <label class="form-check-label text-white" for="aletiq">Aletiq plans</label>
            </div>
        </div>
    </div>
</nav>

{# Style personnalisé #}
<style>
    /* Style du texte dans la nav */
    #navbarSupportedContent .nav-link {
        color: white;
        font-weight: 500;
    }
    #navbarSupportedContent .nav-link:hover {
        color: rgb(204, 204, 204);
    }
    .nav-item, .dropdown-menu li {
        position: relative;
        margin-right: 10px;
    }
    /* Style pour l'élément actif */
    .nav-link.active,
    .dropdown-menu li.active > .dropdown-item {
        color:rgb(182, 216, 127) !important;
        font-weight: bold;
    }

    .dropdown-menu li{
        margin: 0px;
    }
    .dropdown-menu span{
        margin-right: 10px;
    }

    #aletiq:checked {
    background-color: green;
    border-color: green;
    }
</style>

<script>
    $(document).ready(function() {

        let storedState = localStorage.getItem('aletiq');

        // Si 'true', on coche le switch
        if (storedState === 'true') {
            $('#aletiq').prop('checked', true);
        }

        // Au changement d'état, on enregistre dans le localStorage
       $('#aletiq').on('change', function () {
            const isChecked = $(this).is(':checked');
            localStorage.setItem('aletiq', isChecked);

            if (isChecked) {
                $('.preview-tooltip').each(function () {
                    const url = $(this).attr('href');
                    tippy(this, {
                        content: `<iframe src="${url}" width="800" height="500" style="border: none;"></iframe>`,
                        allowHTML: true,
                        interactive: true,
                        placement: 'right',
                        theme: 'light-border'
                    });
                });
            } else {
                // Détruire tous les tooltips Tippy actifs
                document.querySelectorAll('.preview-tooltip').forEach(el => {
                    if (el._tippy) {
                        el._tippy.destroy();
                    }
                });
            }
        });

        // AJAX simple pour les badges - SANS CACHE CLIENT
        $.ajax({
            url: "{{ path('count_document') }}",
            method: 'GET',
            dataType: 'json',
            timeout: 10000,
            success: function(data) {
                // Ajout des badges sur chaque élément individuel
                $.each(data, function(step, count) {
                    if (count > 0) {
                        const $element = $('[data-step="' + step + '"]');
                        if ($element.length) {
                            const $badge = $('<span>')
                                .addClass('badge rounded-pill bg-success position-absolute')
                                .css({
                                    top: '8px',
                                    right: '0px',
                                    transform: 'translate(50%, -50%)',
                                    fontSize: '0.65rem'
                                })
                                .text(count);
                            $element.append($badge);
                        }
                    }
                });

                // Pour chaque dropdown, sommer les valeurs
                $('.nav-item.dropdown').each(function() {
                    let total = 0;
                    $(this).find('li[data-step]').each(function() {
                        const step = $(this).data('step');
                        if (data[step] && data[step] > 0) {
                            total += data[step];
                        }
                    });

                    if (total > 0) {
                        const $parentLink = $(this).children('a.dropdown-toggle');
                        const $badge = $('<span>')
                            .addClass('badge rounded-pill bg-success position-absolute')
                            .css({
                                top: '8px',
                                right: '0px',
                                transform: 'translate(50%, -50%)',
                                fontSize: '0.65rem'
                            })
                            .text(total);
                        $parentLink.append($badge);
                    }
                });

                console.log('Badges appliqués:', Object.keys(data).length);
            },
            error: function(xhr, status, error) {
                console.error('Erreur chargement badges:', error);
            }
        });
    });
</script>
