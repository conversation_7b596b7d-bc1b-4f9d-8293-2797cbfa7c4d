<?php

namespace App\Controller;

use App\Repository\DocumentRepository;
use App\Service\DataAnalysisService;
use App\Service\AlertService;
use App\Service\BottleneckAnalysisService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/forecast')]
class ForecastController extends AbstractController
{
    private DocumentRepository $documentRepository;
    private DataAnalysisService $dataAnalysisService;
    private AlertService $alertService;
    private BottleneckAnalysisService $bottleneckAnalysisService;

    public function __construct(
        DocumentRepository $documentRepository,
        DataAnalysisService $dataAnalysisService,
        AlertService $alertService,
        BottleneckAnalysisService $bottleneckAnalysisService
    ) {
        $this->documentRepository = $documentRepository;
        $this->dataAnalysisService = $dataAnalysisService;
        $this->alertService = $alertService;
        $this->bottleneckAnalysisService = $bottleneckAnalysisService;
    }

    #[Route('/', name: 'app_forecast', methods: ['GET'])]
    public function index(): Response
    {
        // Récupérer les tendances des temps de traitement
        $processingTimeTrends = $this->dataAnalysisService->analyzeProcessingTimeTrends('month', 12);

        // Récupérer les documents à risque
        $riskyDocuments = $this->dataAnalysisService->identifyRiskyDocuments();

        // Récupérer les statistiques optimisées sans charger tous les documents
        $documentStats = $this->documentRepository->getDocumentStatsOptimized();
        $activeDocuments = $documentStats['active_count'];
        $completedDocuments = $documentStats['completed_count'];
        $totalDocuments = $documentStats['total_count'];

        // Calculer les statistiques par type de document de manière optimisée
        $docTypeStats = $this->documentRepository->getDocTypeStatsOptimized();

        // Calculer les prévisions pour les prochains mois
        $forecast = $this->calculateForecast($processingTimeTrends);

        return $this->render('forecast/index.html.twig', [
            'processing_time_trends' => $processingTimeTrends,
            'risky_documents' => $riskyDocuments,
            'doc_type_stats' => $docTypeStats,
            'forecast' => $forecast,
            'total_documents' => $totalDocuments,
            'active_documents' => $activeDocuments,
            'completed_documents' => $completedDocuments,
        ]);
    }

    #[Route('/document-prediction/{id}', name: 'app_forecast_document', methods: ['GET'])]
    public function documentPrediction(int $id): Response
    {
        $document = $this->documentRepository->find($id);

        if (!$document) {
            throw $this->createNotFoundException('Document non trouvé');
        }

        // Prédire le temps de traitement pour ce document
        $prediction = $this->dataAnalysisService->predictProcessingTime($document);

        return $this->render('forecast/document_prediction.html.twig', [
            'document' => $document,
            'prediction' => $prediction,
        ]);
    }

    #[Route('/api/trends', name: 'app_forecast_api_trends', methods: ['GET'])]
    public function apiTrends(Request $request): JsonResponse
    {
        $period = $request->query->get('period', 'month');
        $limit = $request->query->getInt('limit', 12);
        $docType = $request->query->get('doc_type');

        $trends = $this->dataAnalysisService->analyzeProcessingTimeTrends($period, $limit, $docType);

        return new JsonResponse($trends);
    }

    #[Route('/api/period-details', name: 'app_forecast_api_period_details', methods: ['GET'])]
    public function apiPeriodDetails(Request $request): JsonResponse
    {
        $periodKey = $request->query->get('period');
        $period = $request->query->get('period_type', 'month');

        if (!$periodKey) {
            return new JsonResponse(['error' => 'Period parameter is required'], 400);
        }

        $details = $this->dataAnalysisService->getPeriodDetails($periodKey, $period);

        return new JsonResponse($details);
    }

    #[Route('/api/alerts', name: 'app_forecast_api_alerts', methods: ['GET'])]
    public function apiAlerts(): JsonResponse
    {
        $alerts = $this->alertService->generateAlerts();
        return new JsonResponse($alerts);
    }

    #[Route('/api/bottlenecks', name: 'app_forecast_api_bottlenecks', methods: ['GET'])]
    public function apiBottlenecks(): JsonResponse
    {
        $analysis = $this->bottleneckAnalysisService->analyzeBottlenecks();
        return new JsonResponse($analysis);
    }

    #[Route('/api/bottlenecks/states', name: 'app_forecast_api_bottlenecks_states', methods: ['GET'])]
    public function apiBottleneckStates(): JsonResponse
    {
        $stateAnalysis = $this->bottleneckAnalysisService->analyzeStateBottlenecks();
        return new JsonResponse($stateAnalysis);
    }

    #[Route('/api/bottlenecks/teams', name: 'app_forecast_api_bottlenecks_teams', methods: ['GET'])]
    public function apiBottleneckTeams(): JsonResponse
    {
        $teamAnalysis = $this->bottleneckAnalysisService->analyzeTeamBottlenecks();
        return new JsonResponse($teamAnalysis);
    }



    /**
     * Calcule les prévisions pour les prochains mois
     */
    private function calculateForecast(array $trends): array
    {
        $forecast = [];

        // Extraire les données historiques
        $historicalData = [];
        foreach ($trends as $period => $data) {
            $historicalData[] = $data['avg_processing_time'];
        }

        // Si nous avons moins de 3 points de données, impossible de faire une prévision fiable
        if (count($historicalData) < 3) {
            return $forecast;
        }

        // Calculer la tendance (moyenne mobile sur 3 périodes)
        $movingAverage = [];
        for ($i = 2; $i < count($historicalData); $i++) {
            $movingAverage[] = ($historicalData[$i] + $historicalData[$i-1] + $historicalData[$i-2]) / 3;
        }

        // Calculer la tendance linéaire
        $n = count($movingAverage);
        $sumX = 0;
        $sumY = 0;
        $sumXY = 0;
        $sumX2 = 0;

        for ($i = 0; $i < $n; $i++) {
            $x = $i + 1;
            $y = $movingAverage[$i];

            $sumX += $x;
            $sumY += $y;
            $sumXY += $x * $y;
            $sumX2 += $x * $x;
        }

        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumX2 - $sumX * $sumX);
        $intercept = ($sumY - $slope * $sumX) / $n;

        // Générer les prévisions pour les 3 prochaines périodes
        $lastPeriod = array_key_last($trends);
        $lastData = $trends[$lastPeriod];

        for ($i = 1; $i <= 3; $i++) {
            $nextX = $n + $i;
            $predictedValue = $intercept + $slope * $nextX;

            // Assurer que la valeur prédite est positive
            $predictedValue = max(0, $predictedValue);

            // Créer une étiquette pour la période suivante
            $nextPeriod = $this->getNextPeriod($lastPeriod, $i);

            $forecast[$nextPeriod] = [
                'label' => $nextPeriod,
                'predicted_time' => round($predictedValue, 1),
            ];
        }

        return $forecast;
    }

    /**
     * Calcule la période suivante en fonction de la période actuelle
     */
    private function getNextPeriod(string $currentPeriod, int $offset = 1): string
    {
        // Format attendu: MM/YYYY
        $parts = explode('/', $currentPeriod);
        if (count($parts) !== 2) {
            return 'N/A';
        }

        $month = (int)$parts[0];
        $year = (int)$parts[1];

        $month += $offset;

        while ($month > 12) {
            $month -= 12;
            $year++;
        }

        return sprintf('%02d/%d', $month, $year);
    }
}
