<?php

namespace App\Service;

use App\Entity\Document;
use App\Repository\DocumentRepository;
use App\Utils\DocumentConstants;
use Doctrine\ORM\EntityManagerInterface;

class DataAnalysisService
{

    private EntityManagerInterface $entityManager;
    private DocumentRepository $documentRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        DocumentRepository $documentRepository
    ) {
        $this->entityManager = $entityManager;
        $this->documentRepository = $documentRepository;
    }

    /**
     * Analyse les tendances des temps de traitement - Version optimisée
     */
    public function analyzeProcessingTimeTrends(string $period = 'month', int $limit = 6, ?string $docType = null): array
    {
        // Utiliser le cache pour éviter les recalculs fréquents
        $cacheKey = "trends_{$period}_{$limit}_" . ($docType ?? 'all');
        $cached = $this->getCachedResult($cacheKey, 300); // Cache 5 minutes
        if ($cached !== null) {
            return $cached;
        }

        $trends = [];
        $now = new \DateTime();

        // Définir les périodes
        switch ($period) {
            case 'week':
                $interval = 'P1W'; // 1 semaine
                $format = 'W/Y';
                $label = 'Semaine ';
                break;
            case 'month':
                $interval = 'P1M'; // 1 mois
                $format = 'm/Y';
                $label = '';
                break;
            case 'quarter':
                $interval = 'P3M'; // 3 mois
                $format = 'Q/Y';
                $label = 'T';
                break;
            case 'year':
                $interval = 'P1Y'; // 1 an
                $format = 'Y';
                $label = '';
                break;
            default:
                $interval = 'P1M';
                $format = 'm/Y';
                $label = '';
        }

        // Générer les périodes
        $periods = [];
        $startDate = clone $now;

        for ($i = $limit - 1; $i >= 0; $i--) {
            $date = clone $startDate;

            // Soustraire l'intervalle i fois
            if ($i > 0) {
                switch ($period) {
                    case 'week':
                        $date->modify('-' . $i . ' weeks');
                        break;
                    case 'month':
                        $date->modify('-' . $i . ' months');
                        break;
                    case 'quarter':
                        $date->modify('-' . ($i * 3) . ' months');
                        break;
                    case 'year':
                        $date->modify('-' . $i . ' years');
                        break;
                }
            }

            // Générer la clé et le label de période
            if ($period === 'quarter') {
                // Calculer le trimestre manuellement
                $month = (int)$date->format('n');
                $year = $date->format('Y');
                $quarter = ceil($month / 3);
                $periodKey = "T{$quarter}/{$year}";
                $periodLabel = "T{$quarter}/{$year}";
            } else {
                $periodKey = $date->format($format);
                $periodLabel = $label . $date->format($format);
            }

            $periods[$periodKey] = [
                'label' => $periodLabel,
                'start_date' => clone $date,
                'end_date' => clone $date,
                'avg_processing_time' => 0,
                'document_count' => 0,
                'total_time' => 0,
            ];

            // Ajuster la date de fin pour couvrir toute la période
            switch ($period) {
                case 'week':
                    $periods[$periodKey]['end_date']->modify('+6 days');
                    break;
                case 'month':
                    $periods[$periodKey]['end_date']->modify('last day of this month');
                    break;
                case 'quarter':
                    // Pour les trimestres, ajuster les dates de début et fin
                    $month = (int)$date->format('n');
                    $year = (int)$date->format('Y');
                    $quarter = ceil($month / 3);
                    $startMonth = ($quarter - 1) * 3 + 1;
                    $endMonth = $quarter * 3;

                    $periods[$periodKey]['start_date'] = new \DateTime("$year-$startMonth-01");
                    $periods[$periodKey]['end_date'] = new \DateTime("$year-$endMonth-01");
                    $periods[$periodKey]['end_date']->modify('last day of this month');
                    break;
                case 'year':
                    $periods[$periodKey]['end_date']->modify('last day of december this year');
                    break;
            }
        }

        // Récupérer tous les documents
        $documents = $this->documentRepository->findAll();

        foreach ($documents as $document) {
            // Pour l'analyse, ne considérer que les documents terminés
            if (!$this->isDocumentCompleted($document)) {
                continue;
            }

            // Filtrer par type de document si spécifié
            if ($docType && $document->getDocType() !== $docType) {
                continue;
            }

            $timestamps = $document->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }

            // Calculer le temps de traitement basé sur les visas BE_0 et Costing
            $firstDate = $document->getVisaDate('visa_BE_0');
            $lastDate = $document->getVisaDate('visa_Costing');

            // Convertir en DateTime si nécessaire
            if ($firstDate instanceof \DateTimeImmutable) {
                $firstDate = \DateTime::createFromImmutable($firstDate);
            }
            if ($lastDate instanceof \DateTimeImmutable) {
                $lastDate = \DateTime::createFromImmutable($lastDate);
            }

            if ($firstDate && $lastDate) {
                $diff = $lastDate->diff($firstDate);
                $processingTime = $diff->days;

                // Attribuer à la période correspondante
                foreach ($periods as $periodKey => &$periodData) {
                    if ($firstDate >= $periodData['start_date'] && $firstDate <= $periodData['end_date']) {
                        $periodData['document_count']++;
                        $periodData['total_time'] += $processingTime;
                        break;
                    }
                }
            }
        }

        // Calculer les moyennes
        foreach ($periods as $periodKey => &$periodData) {
            if ($periodData['document_count'] > 0) {
                $periodData['avg_processing_time'] = round($periodData['total_time'] / $periodData['document_count'], 1);
            }
        }

        return $periods;
    }

    /**
     * Récupère les détails d'une période spécifique
     */
    public function getPeriodDetails(string $periodKey, string $period = 'month'): array
    {
        $now = new \DateTime();

        // Définir le format selon la période
        switch ($period) {
            case 'week':
                $format = 'W/Y';
                break;
            case 'month':
                $format = 'm/Y';
                break;
            case 'quarter':
                $format = 'Q/Y';
                break;
            case 'year':
                $format = 'Y';
                break;
            default:
                $format = 'm/Y';
        }

        // Calculer les dates de début et fin de la période
        $startDate = null;
        $endDate = null;

        if ($period === 'month') {
            $parts = explode('/', $periodKey);
            if (count($parts) === 2) {
                $month = (int)$parts[0];
                $year = (int)$parts[1];
                $startDate = new \DateTime("$year-$month-01");
                $endDate = clone $startDate;
                $endDate->modify('last day of this month');
            }
        } elseif ($period === 'year') {
            $year = (int)$periodKey;
            $startDate = new \DateTime("$year-01-01");
            $endDate = new \DateTime("$year-12-31");
        } elseif ($period === 'week') {
            $parts = explode('/', $periodKey);
            if (count($parts) === 2) {
                $week = (int)$parts[0];
                $year = (int)$parts[1];
                $startDate = new \DateTime();
                $startDate->setISODate($year, $week);
                $endDate = clone $startDate;
                $endDate->modify('+6 days');
            }
        } elseif ($period === 'quarter') {
            // Le format peut être "Q1/2024" ou "1/2024" pour les trimestres
            $parts = explode('/', $periodKey);
            if (count($parts) === 2) {
                $quarterStr = $parts[0];
                $year = (int)$parts[1];

                // Extraire le numéro du trimestre (enlever le "Q" ou "T" s'il existe)
                $quarter = (int)preg_replace('/[^0-9]/', '', $quarterStr);

                // Valider le trimestre (1-4)
                if ($quarter >= 1 && $quarter <= 4) {
                    $startMonth = ($quarter - 1) * 3 + 1;
                    $endMonth = $quarter * 3;
                    $startDate = new \DateTime("$year-$startMonth-01");
                    $endDate = new \DateTime("$year-$endMonth-01");
                    $endDate->modify('last day of this month');
                }
            }
        }

        if (!$startDate || !$endDate) {
            return [
                'period' => $periodKey,
                'documents' => [],
                'total_documents' => 0,
                'total_processing_time' => 0,
                'avg_processing_time' => 0,
                'by_place' => [],
                'by_doc_type' => []
            ];
        }

        // Récupérer tous les documents terminés dans cette période
        $documents = $this->documentRepository->findAll();
        $periodDocuments = [];
        $totalTime = 0;
        $byPlace = [];
        $byDocType = [];

        foreach ($documents as $document) {
            // Pour l'analyse, ne considérer que les documents terminés
            if (!$this->isDocumentCompleted($document)) {
                continue;
            }

            $timestamps = $document->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }

            // Calculer le temps de traitement basé sur les visas BE_0 et Costing
            $firstDate = $document->getVisaDate('visa_BE_0');
            $lastDate = $document->getVisaDate('visa_Costing');

            // Convertir en DateTime si nécessaire
            if ($firstDate instanceof \DateTimeImmutable) {
                $firstDate = \DateTime::createFromImmutable($firstDate);
            }
            if ($lastDate instanceof \DateTimeImmutable) {
                $lastDate = \DateTime::createFromImmutable($lastDate);
            }

            // Vérifier si le document appartient à cette période
            if ($firstDate && $firstDate >= $startDate && $firstDate <= $endDate) {
                if ($lastDate) {
                    $diff = $lastDate->diff($firstDate);
                    $processingTime = $diff->days;
                    $totalTime += $processingTime;

                    // Analyser par place (états actuels)
                    $currentSteps = $document->getCurrentSteps();
                    if ($currentSteps) {
                        foreach ($currentSteps as $state => $value) {
                            if (!isset($byPlace[$state])) {
                                $byPlace[$state] = [
                                    'count' => 0,
                                    'total_time' => 0,
                                    'avg_time' => 0
                                ];
                            }
                            $byPlace[$state]['count']++;
                            $byPlace[$state]['total_time'] += $processingTime;
                        }
                    }

                    // Analyser par type de document
                    $docType = $document->getDocType() ?: 'N/A';
                    if (!isset($byDocType[$docType])) {
                        $byDocType[$docType] = [
                            'count' => 0,
                            'total_time' => 0,
                            'avg_time' => 0
                        ];
                    }
                    $byDocType[$docType]['count']++;
                    $byDocType[$docType]['total_time'] += $processingTime;

                    $periodDocuments[] = [
                        'id' => $document->getId(),
                        'reference' => $document->getReference(),
                        'doc_type' => $docType,
                        'processing_time' => $processingTime,
                        'first_date' => $firstDate->format('Y-m-d'),
                        'last_date' => $lastDate->format('Y-m-d'),
                        'current_steps' => array_keys($currentSteps ?: [])
                    ];
                }
            }
        }

        // Calculer les moyennes
        foreach ($byPlace as $place => &$data) {
            if ($data['count'] > 0) {
                $data['avg_time'] = round($data['total_time'] / $data['count'], 1);
            }
        }

        foreach ($byDocType as $type => &$data) {
            if ($data['count'] > 0) {
                $data['avg_time'] = round($data['total_time'] / $data['count'], 1);
            }
        }

        $result = [
            'period' => $periodKey,
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'documents' => $periodDocuments,
            'total_documents' => count($periodDocuments),
            'total_processing_time' => $totalTime,
            'avg_processing_time' => count($periodDocuments) > 0 ? round($totalTime / count($periodDocuments), 1) : 0,
            'by_place' => $byPlace,
            'by_doc_type' => $byDocType
        ];

        // Mettre en cache le résultat
        $this->setCachedResult($cacheKey, $result);

        return $result;
    }

    /**
     * Prédit le temps de traitement pour un document - Version optimisée
     */
    public function predictProcessingTime(Document $document): array
    {
        $docType = $document->getDocType();
        $procType = $document->getProcType();
        $materialType = $document->getMaterialType();

        // Utiliser le cache pour éviter les recalculs
        $cacheKey = "prediction_{$docType}_{$procType}_{$materialType}";
        $cached = $this->getCachedResult($cacheKey, 600); // Cache 10 minutes
        if ($cached !== null) {
            return $cached;
        }

        // Récupérer des documents similaires avec limite
        $similarDocuments = $this->documentRepository->findSimilarDocuments($docType, $procType, $materialType, 100);

        $totalTime = 0;
        $count = 0;
        $minTime = PHP_INT_MAX;
        $maxTime = 0;

        foreach ($similarDocuments as $similarDoc) {
            // Pour la prédiction, ne considérer que les documents terminés
            if (!$this->isDocumentCompleted($similarDoc)) {
                continue;
            }

            $timestamps = $similarDoc->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }

            // Calculer le temps de traitement basé sur les visas BE_0 et Costing
            $firstDate = $similarDoc->getVisaDate('visa_BE_0');
            $lastDate = $similarDoc->getVisaDate('visa_Costing');

            // Convertir en DateTime si nécessaire
            if ($firstDate instanceof \DateTimeImmutable) {
                $firstDate = \DateTime::createFromImmutable($firstDate);
            }
            if ($lastDate instanceof \DateTimeImmutable) {
                $lastDate = \DateTime::createFromImmutable($lastDate);
            }

            if ($firstDate && $lastDate) {
                $diff = $lastDate->diff($firstDate);
                $processingTime = $diff->days;
                $totalTime += $processingTime;
                $count++;

                $minTime = min($minTime, $processingTime);
                $maxTime = max($maxTime, $processingTime);
            }
        }

        $avgTime = $count > 0 ? round($totalTime / $count, 1) : null;

        $result = [
            'avg_time' => $avgTime,
            'min_time' => $minTime < PHP_INT_MAX ? $minTime : null,
            'max_time' => $maxTime > 0 ? $maxTime : null,
            'sample_size' => $count,
        ];

        // Mettre en cache le résultat
        $this->setCachedResult($cacheKey, $result);

        return $result;
    }

    /**
     * Identifie les documents à risque (stagnants) - Version optimisée
     */
    public function identifyRiskyDocuments(int $thresholdDays = 7, int $limit = 50): array
    {
        $riskyDocuments = [];
        $now = new \DateTime();

        // États considérés comme "paniers" - documents dans ces états ne sont pas à risque
        $panierStates = DocumentConstants::PANIER_STATES;

        // Utiliser une requête optimisée pour récupérer seulement les documents actifs récents
        $documents = $this->documentRepository->findActiveDocumentsForRiskAnalysis($limit);

        foreach ($documents as $document) {
            $currentSteps = $document->getCurrentSteps();
            if (!$currentSteps) {
                continue;
            }

            $timestamps = $document->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }

            foreach ($currentSteps as $state => $value) {
                // Exclure les documents dans un état panier
                if (in_array($state, $panierStates)) {
                    continue;
                }

                // Exclure les documents terminés (qui ont le visa correspondant à leur état actuel)
                if ($this->isDocumentCompletedForState($document, $state)) {
                    continue;
                }

                if (!isset($timestamps[$state])) {
                    continue;
                }

                $entries = $timestamps[$state];
                $lastEntry = null;

                if (is_array($entries)) {
                    $lastEntry = end($entries);
                    if (isset($lastEntry['enter'])) {
                        $enterDate = new \DateTime($lastEntry['enter']);
                    } else {
                        continue;
                    }
                } elseif (is_string($entries)) {
                    $enterDate = new \DateTime($entries);
                } else {
                    continue;
                }

                $diff = $now->diff($enterDate);
                $daysSinceEnter = $diff->days;

                if ($daysSinceEnter >= $thresholdDays) {
                    $riskyDocuments[] = [
                        'document' => $document,
                        'state' => $state,
                        'days_in_state' => $daysSinceEnter,
                        'enter_date' => $enterDate,
                    ];
                }
            }
        }

        // Trier par nombre de jours décroissant
        usort($riskyDocuments, function($a, $b) {
            return $b['days_in_state'] - $a['days_in_state'];
        });

        return $riskyDocuments;
    }

    /**
     * Vérifie si un document est terminé pour un état donné
     * Un état est terminé s'il a le visa correspondant ou si c'est un état panier
     */
    private function isDocumentCompletedForState(Document $document, string $state): bool
    {
        // États considérés comme "paniers" - documents dans ces états sont considérés comme terminés
        $panierStates = DocumentConstants::PANIER_STATES;

        // Si le document est dans un état panier, il est considéré comme terminé
        if (in_array($state, $panierStates)) {
            return true;
        }

        // Vérifier si le document a le visa correspondant à l'état
        $visaName = 'visa_' . $state;

        // Cas spécial pour les états logistiques
        if ($state === 'Qual_Logistique' || $state === 'Logistique') {
            // Pour les états logistiques, vérifier si les deux visas sont présents
            return $document->hasVisa('visa_Qual_Logistique') && $document->hasVisa('visa_Logistique');
        }

        return $document->hasVisa($visaName);
    }

    /**
     * Vérifie si un document est globalement terminé
     * Utilisé pour la partie analyse
     *
     * Un document est terminé s'il a le visa Costing (fin du workflow).
     * Pour les analyses de temps, on ne considère que les documents avec visa_BE_0 ET visa_Costing.
     */
    public function isDocumentCompleted(Document $document): bool
    {
        // Un document est terminé s'il a le visa Costing
        $hasCosting = $document->hasVisa('visa_Costing');
        $hasBE0 = $document->hasVisa('visa_BE_0');

        // Pour les analyses, on ne considère que les documents qui ont un cycle complet
        return $hasCosting && $hasBE0;
    }

    /**
     * Cache simple en mémoire pour éviter les recalculs
     */
    private static $cache = [];

    /**
     * Récupère un résultat du cache s'il est encore valide
     */
    private function getCachedResult(string $key, int $ttlSeconds): ?array
    {
        if (!isset(self::$cache[$key])) {
            return null;
        }

        $cached = self::$cache[$key];
        $now = time();

        if (($now - $cached['timestamp']) > $ttlSeconds) {
            unset(self::$cache[$key]);
            return null;
        }

        return $cached['data'];
    }

    /**
     * Met en cache un résultat
     */
    private function setCachedResult(string $key, array $data): void
    {
        self::$cache[$key] = [
            'data' => $data,
            'timestamp' => time()
        ];
    }
}
